{"name": "menli-app", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"expo": "~53.0.22", "expo-status-bar": "~2.2.3", "nativewind": "^4.1.23", "prettier-plugin-tailwindcss": "^0.5.14", "react": "19.0.0", "react-native": "0.79.6", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.4.0", "tailwindcss": "^3.4.17"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}